import numpy as np
import networkx as nx
import time  # 添加time模块
from tqdm import tqdm  # 添加tqdm导入
from base_fun import gen_graph, IC, visualize_seed_set # 从base_fun导入图构建和IC影响力计算函数

# ====== 阶段1：确定初始聚类中心 ======

def calculate_kshell(G):
    """使用k-core分解方法计算所有节点的K-shell值"""
    ks = {}
    k_core = nx.core_number(G)
    for node in G.nodes():
        ks[node] = k_core[node]
    return ks

def calculate_mass(G, ks):
    """
    使用公式(2)计算每个节点的质量
    节点质量 = 节点的K-shell值 + 所有邻居的K-shell值之和
    """
    mass = {}
    for node in G.nodes():
        neighbors = list(G.neighbors(node))
        node_mass = ks[node]
        for neighbor in neighbors:
            node_mass += ks[neighbor]
        mass[node] = node_mass
    return mass

def calculate_potential(G, mass, sigma=1.1243):
    """使用公式(1)计算每个节点的拓扑势能"""
    potential = {}
    
    # 计算影响范围
    # r = int(np.floor(3 * sigma / np.sqrt(2)))
    r = 1
    
    for node in G.nodes():
        # 修正的BFS逻辑，从直接邻居开始
        visited = {node}
        current_level = set(G.neighbors(node))
        r_neighbors = current_level.copy()
        
        # 继续遍历剩余的r-1层
        for level in range(r - 1):
            if not current_level:
                break
            next_level = set()
            for n in current_level:
                neighbors = set(G.neighbors(n)) - visited
                next_level.update(neighbors)
            visited.update(next_level)
            r_neighbors.update(next_level)
            current_level = next_level
        
        # 计算拓扑势能
        pot = 0
        for neighbor in r_neighbors:
            try:
                distance = nx.shortest_path_length(G, node, neighbor)
                pot += mass[neighbor] * np.exp(-(distance / sigma) ** 2)
            except nx.NetworkXNoPath:
                continue
        
        potential[node] = pot
    
    return potential

def categorize_nodes(G, potential):
    """基于拓扑势能将节点分类为山峰、山谷或斜坡"""
    node_types = {}
    peak_count = 0
    valley_count = 0
    slope_count = 0
    
    for node in G.nodes():
        neighbors = list(G.neighbors(node))
        
        # 检查节点是否为山峰（定义1）：拓扑势能大于或等于所有邻居
        is_peak = all(potential[node] >= potential[neighbor] for neighbor in neighbors)
        
        # 检查节点是否为山谷（定义2）：拓扑势能小于或等于所有邻居
        is_valley = all(potential[node] <= potential[neighbor] for neighbor in neighbors)
        
        # 检查节点是否为斜坡（定义3）
        has_greater = any(potential[neighbor] > potential[node] for neighbor in neighbors)
        has_smaller = any(potential[neighbor] < potential[node] for neighbor in neighbors)
        is_slope = has_greater and has_smaller
        
        # 对节点进行分类并计数
        if is_peak:
            node_types[node] = 'peak'
            peak_count += 1
        elif is_valley:
            node_types[node] = 'valley'
            valley_count += 1
        elif is_slope:
            node_types[node] = 'slope'
            slope_count += 1
        else:
            print(f"节点 {node} 既不是山峰、也不是山谷、也不是斜坡")
            node_types[node] = 'slope'
            slope_count += 1
    
    # 输出统计结果
    # print(f"节点类型统计:")
    print(f"山峰节点数量: {peak_count}")
    print(f"山谷节点数量: {valley_count}")
    print(f"斜坡节点数量: {slope_count}")
    # print(f"总节点数量: {len(G.nodes())}")
                
    return node_types

def calculate_minimum_distance(G, cs, potential):
    """使用公式(3)计算每个节点的最小距离"""
    theta = {}
    
    if len(cs) <= 1:  # 处理候选集大小为0或1的特殊情况
        if cs:
            theta[cs[0]] = 1
        return theta
    
    # 找到最高势能节点
    max_potential_node = max(cs, key=lambda x: potential[x])
    
    # 计算非最高势能节点的最小距离
    for node in cs:
        if node != max_potential_node:
            greater_nodes = [n for n in cs if potential[n] > potential[node]]
            if greater_nodes:
                min_dist = float('inf')
                for greater_node in greater_nodes:
                    try:
                        dist = nx.shortest_path_length(G, node, greater_node)
                        min_dist = min(min_dist, dist)
                    except nx.NetworkXNoPath:
                        continue
                
                theta[node] = min_dist if min_dist != float('inf') else 1
            else:
                theta[node] = 1
    
    # 计算最高势能节点的最小距离
    other_distances = [theta[n] for n in cs if n != max_potential_node]
    theta[max_potential_node] = max(other_distances) if other_distances else 1
    
    return theta

def calculate_synthetic_value(cs, potential, theta):
    """使用公式(4)计算候选集中每个节点的综合值"""
    sv = {}
    for node in cs:
        sv[node] = potential[node] * theta[node]
    return sv

def calculate_node_score(G, node, potential):
    """计算节点的综合得分（势能×全局最小距离）"""
    # 获取所有影响力更高的节点
    higher_nodes = [n for n in G.nodes() if potential[n] > potential[node]]
    
    if not higher_nodes:
        return potential[node]  # 如果没有更高影响力节点，返回势能值
    
    # 计算到所有更高影响力节点的最小距离
    min_dist = float('inf')
    for higher_node in higher_nodes:
        try:
            dist = nx.shortest_path_length(G, node, higher_node)
            min_dist = min(min_dist, dist)
        except nx.NetworkXNoPath:
            continue
    
    # 如果无法到达任何更高影响力节点，使用默认距离1
    min_dist = 1 if min_dist == float('inf') else min_dist
    return potential[node] * min_dist

def phase1_determine_initial_centers(G, k, potential, node_types):
    """阶段1：确定初始聚类中心（算法2）"""
    # 构建候选集（所有山峰节点）
    candidate_set = [node for node, node_type in node_types.items() if node_type == 'peak']
    
    # 修正候选集补充逻辑
    if len(candidate_set) < k:
        print(f"警告：候选集中的山峰节点数量({len(candidate_set)})少于请求的种子数量({k})，将按综合指标补充节点")
        remaining_nodes = [n for n in G.nodes() if n not in candidate_set]
        remaining_nodes.sort(key=lambda x: calculate_node_score(G, x, potential), reverse=True)
        remaining_needed = k - len(candidate_set)
        candidate_set.extend(remaining_nodes[:remaining_needed])
    
    # 计算候选集的综合值
    theta = calculate_minimum_distance(G, candidate_set, potential)
    synthetic_values = calculate_synthetic_value(candidate_set, potential, theta)
    
    # 强制选择k个初始聚类中心
    initial_centers = sorted(synthetic_values.keys(), key=lambda x: synthetic_values[x], reverse=True)[:k]
    print(f"初始聚类中心数量 ： {len(candidate_set)}")
    # print(f"初始聚类中心选择：{initial_centers}")
    # influence_test = IC(G, initial_centers, p=0.1, mc=1000)
    # print(f"初始聚类中心影响力：{influence_test}")
    
    return initial_centers, synthetic_values

# ====== 阶段2：构建节点相似度矩阵 ======

def construct_similarity_matrix(G, nodes):
    """使用TNS指标（公式5-8）构建节点相似度矩阵（优化版）"""
    N = len(nodes)
    sim_matrix = np.zeros((N, N))
    node_dict = {node: idx for idx, node in enumerate(nodes)}
    degree_dict = dict(G.degree())
    
    print("正在构建相似度矩阵...")
    # 预存二级邻居信息（用于三步路径计算）
    second_neighbors = {node: set() for node in nodes}
    for node in nodes:
        for neighbor in G.neighbors(node):
            second_neighbors[node].update(G.neighbors(neighbor))
        second_neighbors[node].discard(node)
    
    for i in range(N):
        node_i = nodes[i]
        d_i = degree_dict[node_i]
        neighbors_i = set(G.neighbors(node_i))
        
        for j in range(i+1, N):
            node_j = nodes[j]
            d_j = degree_dict[node_j]
            
            # 一步路径相似度
            sim1 = 1 / min(d_i, d_j) if G.has_edge(node_i, node_j) else 0
            
            # 两步路径相似度（使用集合交集加速）
            neighbors_j = set(G.neighbors(node_j))
            common_neighbors = neighbors_i & neighbors_j
            sim2 = sum(1/(min(d_i, d_j) * degree_dict[cn]) for cn in common_neighbors)
            
            # 三步路径相似度（利用预存的二级邻居）
            valid_paths = second_neighbors[node_i] & neighbors_j
            sim3 = sum(1/(min(d_i, d_j) * degree_dict[x] * degree_dict[y]) 
                       for x in G.neighbors(node_i) 
                       for y in G.neighbors(x) if y in valid_paths)
            
            total_sim = sim1 + sim2 + sim3
            sim_matrix[i][j] = total_sim
            sim_matrix[j][i] = total_sim  # 对称位置赋值
            
    np.fill_diagonal(sim_matrix, 1.0)  # 对角线填充1.0
    return sim_matrix

# ====== 阶段3：递归聚类和种子选择 ======

def calculate_regional_influence(community, nodes, sim_matrix, potential):
    """使用公式(9)计算社区内节点的区域影响力"""
    ri = {}
    community_set = set(community)
    
    for node in community_set:
        if len(community_set) == 1:
            # 处理孤立节点
            ri[node] = potential[node]
        else:
            node_idx = nodes.index(node)
            influence = 0
            
            # 计算与同一社区内其他节点的相似度
            for other_node in community_set:
                if other_node != node:
                    other_idx = nodes.index(other_node)
                    influence += sim_matrix[node_idx][other_idx]
            
            ri[node] = influence
    
    return ri

def phase3_recursive_clustering(G, k, initial_centers, nodes, sim_matrix, potential):
    """阶段3：递归聚类和种子节点选择（优化版）"""
    node_dict = {node: idx for idx, node in enumerate(nodes)}
    centers = initial_centers.copy()
    communities = [set([c]) for c in centers]
    centers_set = set(centers)
    
    print("正在进行递归聚类...")
    center_indices = [node_dict[c] for c in centers]
    similarity_matrix = sim_matrix[:, center_indices]
    node_indices = np.array([node_dict[node] for node in G.nodes() if node not in centers_set])
    max_iter = 50
    no_change_count = 0
    old_centers = centers.copy()
    
    for iteration in range(max_iter):
        # 优化点3：向量化计算最佳聚类分配
        best_clusters = np.argmax(similarity_matrix[node_indices], axis=1)
        
        # 优化点4：批量更新社区成员
        communities = [set() for _ in range(len(centers))]
        for idx, cluster in enumerate(best_clusters):
            node = nodes[node_indices[idx]]
            communities[cluster].add(node)
        
        # 优化点5：并行计算区域影响力
        # 删除旧的新中心生成代码 ===
        # 删除以下两行：
        # regional_influence = [
        #     np.sum(sim_matrix[node_dict[c], [node_dict[n] for n in comm]]) 
        #     for comm, c in zip(communities, centers)
        # ]
        # new_centers = [max(zip(comm, regional_influence[i]), key=lambda x: x[1])[0] 
        #               for i, comm in enumerate(communities)]
        
        # === 保留新的新中心生成逻辑 ===
        new_centers = []
        for i, comm in enumerate(communities):
            if not comm:  # 处理空社区
                new_centers.append(centers[i])
                continue
            
            # 计算社区内每个节点的区域影响力
            node_influences = {}
            for node in comm:
                node_idx = node_dict[node]
                # 计算节点与社区内其他节点的相似度总和
                influence = sum(sim_matrix[node_idx][node_dict[n]] for n in comm if n != node)
                node_influences[node] = influence
            
            # 选择影响力最大的节点作为新中心
            new_center = max(node_influences.items(), key=lambda x: x[1])[0]
            new_centers.append(new_center)
    
        # === 更新收敛判断 ===
        if not new_centers:  
            break
        
        # 提前终止判断（使用集合比较）
        if set(new_centers) == set(centers):
            no_change_count += 1
            if no_change_count >= 2:
                print(f"提前在第{iteration+1}次迭代后收敛")
                break
        else:
            no_change_count = 0
            centers = new_centers
            centers_set = set(centers)
            old_centers = centers.copy()
            
        if no_change_count >= 2:
            break

    return centers[:k]

def find_influential_nodes(G, k, sigma=1.1243):
    """使用CIM算法寻找有影响力的节点"""
    print("\n=== 开始CIM算法执行 ===")
    nodes = list(G.nodes())
    
    #print("\n第1阶段：计算节点属性...")
    ks = calculate_kshell(G)
    mass = calculate_mass(G, ks)
    potential = calculate_potential(G, mass, sigma)
    node_types = categorize_nodes(G, potential)
    initial_centers, synthetic_values = phase1_determine_initial_centers(G, k, potential, node_types)
    
    # print("\n第2阶段：构建节点相似度矩阵...")
    sim_matrix = construct_similarity_matrix(G, nodes)
    
    # print("\n第3阶段：递归聚类和种子选择...")
    seeds = phase3_recursive_clustering(G, k, initial_centers, nodes, sim_matrix, potential)
    
    # print("\n=== CIM算法执行完成 ===")
    return seeds

import random
from typing import Set, List

def compute_stable_influence(
    graph: dict,       # 网络结构，用邻接表表示（例如：{节点: [邻居列表]}）
    seeds: Set[int],   # 初始种子节点集合
    alpha: float,      # 传播概率α
    num_runs: int = 10000  # 独立模拟次数（取平均以减少随机性）
) -> float:
    """
    计算稳定传播影响力 F = A(t_c) / N
    输入:
        - graph: 网络结构（邻接表字典）
        - seeds: 初始种子节点集合
        - alpha: 传播概率
        - num_runs: 独立模拟次数（默认1000次）
    输出:
        - 平均稳定传播影响力（范围 [0, 1]）
    """
    total_influence = 0.0
    n_nodes = len(graph)  # 网络总节点数 N

    for _ in range(num_runs):
        # 初始化激活状态
        activated = set(seeds)
        newly_activated = set(seeds)

        # 迭代传播直到无新节点激活
        while newly_activated:
            current_activated = set()
            # 遍历当前新激活节点的所有邻居
            for node in newly_activated:
                for neighbor in graph.get(node, []):
                    # 如果邻居未激活且激活成功（概率α）
                    if neighbor not in activated and random.random() < alpha:
                        current_activated.add(neighbor)
            # 更新激活状态
            activated.update(current_activated)
            newly_activated = current_activated

        # 累加本次模拟的稳定传播影响力
        total_influence += len(activated) / n_nodes

    # 返回多次模拟的平均结果
    return total_influence / num_runs

def compute_influence_over_time(graph, seeds, alpha, max_steps=1000) -> List[float]:
    activated = set(seeds)
    newly_activated = set(seeds)
    total_nodes = len(graph)
    influence = [len(activated) / total_nodes]
    
    # 修改初始状态输出格式
    print(f"t=0: {influence[0]:.4f}")
    
    for step in range(1, max_steps + 1):
        current_activated = set()
        for node in newly_activated:
            for neighbor in graph.neighbors(node):
                if neighbor not in activated and random.random() < alpha:
                    current_activated.add(neighbor)
        
        if not current_activated:
            break
            
        activated.update(current_activated)
        current_influence = len(activated) / total_nodes
        influence.append(current_influence)
        # 修改每个时间步的输出格式
        print(f"t={step}: {current_influence:.4f}")
        newly_activated = current_activated
    
    # 修改最终结果输出
    t_c = len(influence) - 1
    print(f"\n传播终止时间 t_c={t_c}")
    
    return influence

if __name__ == "__main__":
    # 开始计时
    start_time = time.time()
    
    # 示例用法
    G = gen_graph("D:\\VS\\code\\networks\\email.txt")
    
    # 计算传播概率α（新增代码段）
    # 计算平均度⟨k⟩和二阶矩⟨k²⟩
    degrees = [d for _, d in G.degree()]
    k_avg = sum(degrees) / len(degrees)
    k_sq_avg = sum(d**2 for d in degrees) / len(degrees)
    alpha_c = k_avg / k_sq_avg  # 流行病阈值
    q = 0.002  # 比例系数
    # alpha = q * alpha_c
    alpha = alpha_c
    
    # 寻找有影响力的种子节点
    # k = 30
    # p = k / len(G.nodes()) #定义种子比例p=k/N）来描述种子节点的规模
    p = 0.002
    k = int(p * len(G.nodes()))
    # k=6
    seeds = find_influential_nodes(G, k)
    print(f"选定的种子节点: 数量: {len(set(seeds))}, 节点: {seeds}")
    
    # 计算影响力随时间的变化（修改alpha参数）
    influence = compute_influence_over_time(G, seeds, alpha=alpha)
    # print(f"influence: {influence}")
    
    # 计算并输出总执行时间（秒）
    total_time = int(time.time() - start_time)
    print(f"算法执行时间: {total_time}秒")
    
    # 可视化结果（如需要请取消注释）
    # visualize_seed_set(G, seeds)
