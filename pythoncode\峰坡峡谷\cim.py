import numpy as np
import networkx as nx
import time  # 添加time模块
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
from functools import partial
from base_fun import gen_graph, IC, visualize_seed_set # 从base_fun导入图构建和IC影响力计算函数

# ====== 阶段1：确定初始聚类中心 ======

def calculate_kshell(G):
    """使用k-core分解方法计算所有节点的K-shell值"""
    # 移除自环边
    G.remove_edges_from(nx.selfloop_edges(G))

    ks = {}
    k_core = nx.core_number(G)
    for node in G.nodes():
        ks[node] = k_core[node]
    return ks

def calculate_node_mass(node, G, ks):
    """计算单个节点的质量"""
    neighbors = list(G.neighbors(node))
    node_mass = ks[node]
    for neighbor in neighbors:
        node_mass += ks[neighbor]
    return node, node_mass

def calculate_mass(G, ks, num_cores=None):
    """
    使用公式(2)计算每个节点的质量
    节点质量 = 节点的K-shell值 + 所有邻居的K-shell值之和

    参数:
    G: 图对象
    ks: 节点K-shell值字典
    num_cores: 使用的CPU核心数，None表示使用所有可用核心
    """
    if num_cores is None:
        num_cores = multiprocessing.cpu_count()

    # 如果节点数量很少或只有1个核心，使用串行计算
    if len(G.nodes()) < 1000 or num_cores <= 1:
        mass = {}
        for node in G.nodes():
            node, node_mass = calculate_node_mass(node, G, ks)
            mass[node] = node_mass
        return mass

    # 使用并行计算
    mass = {}
    nodes = list(G.nodes())

    # 创建部分函数，固定G和ks参数
    partial_func = partial(calculate_node_mass, G=G, ks=ks)

    # 使用进程池并行计算
    with ProcessPoolExecutor(max_workers=num_cores) as executor:
        results = list(executor.map(partial_func, nodes))

    # 整合结果
    for node, node_mass in results:
        mass[node] = node_mass

    return mass

def calculate_node_potential(node, G, mass, sigma, all_shortest_paths, r=1):
    """计算单个节点的拓扑势能"""
    # 修正的BFS逻辑，从直接邻居开始
    visited = {node}
    current_level = set(G.neighbors(node))
    r_neighbors = current_level.copy()

    # 继续遍历剩余的r-1层
    for level in range(r - 1):
        if not current_level:
            break
        next_level = set()
        for n in current_level:
            neighbors = set(G.neighbors(n)) - visited
            next_level.update(neighbors)
        visited.update(next_level)
        r_neighbors.update(next_level)
        current_level = next_level

    # 计算拓扑势能
    pot = 0
    for neighbor in r_neighbors:
        try:
            # 使用预计算的最短路径（如果可用）
            if all_shortest_paths:
                distance = all_shortest_paths[node][neighbor]
            else:
                distance = nx.shortest_path_length(G, node, neighbor)
            pot += mass[neighbor] * np.exp(-(distance / sigma) ** 2)
        except (nx.NetworkXNoPath, KeyError):
            continue

    return node, pot

def calculate_potential(G, mass, sigma=1.1243, num_cores=None):
    """
    使用公式(1)计算每个节点的拓扑势能

    参数:
    G: 图对象
    mass: 节点质量字典
    sigma: 影响因子，调节每个节点的影响范围
    num_cores: 使用的CPU核心数，None表示使用所有可用核心
    """
    if num_cores is None:
        num_cores = multiprocessing.cpu_count()

    # 计算影响范围
    r = 1

    # 预计算所有节点对之间的最短路径
    # 对于中小型图，这可以显著提高性能
    if len(G.nodes()) < 5000:  # 对于大型图，可能内存消耗过大
        try:
            all_shortest_paths = dict(nx.all_pairs_shortest_path_length(G))
        except:
            all_shortest_paths = None
    else:
        all_shortest_paths = None

    # 如果节点数量很少或只有1个核心，使用串行计算
    if len(G.nodes()) < 1000 or num_cores <= 1:
        potential = {}
        for node in G.nodes():
            node, pot = calculate_node_potential(node, G, mass, sigma, all_shortest_paths, r)
            potential[node] = pot
        return potential

    # 使用并行计算
    potential = {}
    nodes = list(G.nodes())

    # 创建部分函数，固定参数
    partial_func = partial(calculate_node_potential, G=G, mass=mass, sigma=sigma,
                          all_shortest_paths=all_shortest_paths, r=r)

    # 使用进程池并行计算
    with ProcessPoolExecutor(max_workers=num_cores) as executor:
        results = list(executor.map(partial_func, nodes))

    # 整合结果
    for node, pot in results:
        potential[node] = pot

    return potential

def categorize_single_node(node, G, potential, neighbor_potentials):
    """对单个节点进行分类"""
    neighbors = list(G.neighbors(node))
    if not neighbors:  # 处理孤立节点
        return node, 'slope'

    node_pot = potential[node]
    neighbor_pots = neighbor_potentials[node]

    # 使用NumPy向量化操作进行比较
    is_peak = all(node_pot >= np_pot for np_pot in neighbor_pots)
    is_valley = all(node_pot <= np_pot for np_pot in neighbor_pots)

    if is_peak:
        return node, 'peak'
    elif is_valley:
        return node, 'valley'
    else:
        return node, 'slope'

def categorize_nodes(G, potential, num_cores=None):
    """
    基于拓扑势能将节点分类为山峰、山谷或斜坡

    参数:
    G: 图对象
    potential: 节点势能字典
    num_cores: 使用的CPU核心数，None表示使用所有可用核心
    """
    if num_cores is None:
        num_cores = multiprocessing.cpu_count()

    # 预计算邻居势能
    neighbor_potentials = {}
    for node in G.nodes():
        neighbors = list(G.neighbors(node))
        if neighbors:  # 确保节点有邻居
            neighbor_potentials[node] = [potential[neighbor] for neighbor in neighbors]

    # 如果节点数量很少或只有1个核心，使用串行计算
    if len(G.nodes()) < 1000 or num_cores <= 1:
        node_types = {}
        for node in G.nodes():
            node, node_type = categorize_single_node(node, G, potential, neighbor_potentials)
            node_types[node] = node_type
    else:
        # 使用并行计算
        nodes = list(G.nodes())

        # 创建部分函数，固定参数
        partial_func = partial(categorize_single_node, G=G, potential=potential,
                              neighbor_potentials=neighbor_potentials)

        # 使用进程池并行计算
        with ProcessPoolExecutor(max_workers=num_cores) as executor:
            results = list(executor.map(partial_func, nodes))

        # 整合结果
        node_types = dict(results)

    # 统计各类型节点数量
    peak_count = sum(1 for node_type in node_types.values() if node_type == 'peak')
    valley_count = sum(1 for node_type in node_types.values() if node_type == 'valley')
    slope_count = sum(1 for node_type in node_types.values() if node_type == 'slope')

    # 输出统计结果
    print(f"山峰节点数量: {peak_count}")
    print(f"山谷节点数量: {valley_count}")
    print(f"斜坡节点数量: {slope_count}")

    return node_types

def calculate_minimum_distance(G, cs, potential):
    """使用公式(3)计算每个节点的最小距离"""
    theta = {}

    if len(cs) <= 1:  # 处理候选集大小为0或1的特殊情况
        if cs:
            theta[cs[0]] = 1
        return theta

    # 找到最高势能节点
    max_potential_node = max(cs, key=lambda x: potential[x])

    # 计算非最高势能节点的最小距离
    for node in cs:
        if node != max_potential_node:
            greater_nodes = [n for n in cs if potential[n] > potential[node]]
            if greater_nodes:
                min_dist = float('inf')
                for greater_node in greater_nodes:
                    try:
                        dist = nx.shortest_path_length(G, node, greater_node)
                        min_dist = min(min_dist, dist)
                    except nx.NetworkXNoPath:
                        continue

                theta[node] = min_dist if min_dist != float('inf') else 1
            else:
                theta[node] = 1

    # 计算最高势能节点的最小距离
    other_distances = [theta[n] for n in cs if n != max_potential_node]
    theta[max_potential_node] = max(other_distances) if other_distances else 1

    return theta

def calculate_synthetic_value(cs, potential, theta):
    """使用公式(4)计算候选集中每个节点的综合值"""
    sv = {}
    for node in cs:
        sv[node] = potential[node] * theta[node]
    return sv

def calculate_node_score(G, node, potential):
    """计算节点的综合得分（势能×全局最小距离）"""
    # 获取所有影响力更高的节点
    higher_nodes = [n for n in G.nodes() if potential[n] > potential[node]]

    if not higher_nodes:
        return potential[node]  # 如果没有更高影响力节点，返回势能值

    # 计算到所有更高影响力节点的最小距离
    min_dist = float('inf')
    for higher_node in higher_nodes:
        try:
            dist = nx.shortest_path_length(G, node, higher_node)
            min_dist = min(min_dist, dist)
        except nx.NetworkXNoPath:
            continue

    # 如果无法到达任何更高影响力节点，使用默认距离1
    min_dist = 1 if min_dist == float('inf') else min_dist
    return potential[node] * min_dist

def phase1_determine_initial_centers(G, k, potential, node_types):
    """阶段1：确定初始聚类中心（算法2）"""
    # 构建候选集（所有山峰节点）
    candidate_set = [node for node, node_type in node_types.items() if node_type == 'peak']

    # 修正候选集补充逻辑
    if len(candidate_set) < k:
        print(f"警告：候选集中的山峰节点数量({len(candidate_set)})少于请求的种子数量({k})，将按综合指标补充节点")
        remaining_nodes = [n for n in G.nodes() if n not in candidate_set]
        remaining_nodes.sort(key=lambda x: calculate_node_score(G, x, potential), reverse=True)
        remaining_needed = k - len(candidate_set)
        candidate_set.extend(remaining_nodes[:remaining_needed])

    # 计算候选集的综合值
    theta = calculate_minimum_distance(G, candidate_set, potential)
    synthetic_values = calculate_synthetic_value(candidate_set, potential, theta)

    # 强制选择k个初始聚类中心
    initial_centers = sorted(synthetic_values.keys(), key=lambda x: synthetic_values[x], reverse=True)[:k]
    print(f"初始聚类中心数量 ： {len(candidate_set)}")
    # print(f"初始聚类中心选择：{initial_centers}")
    # influence_test = IC(G, initial_centers, p=0.1, mc=1000)
    # print(f"初始聚类中心影响力：{influence_test}")

    return initial_centers, synthetic_values

# ====== 阶段2：构建节点相似度矩阵 ======

def calculate_node_pair_similarity(pair_data):
    """计算一对节点之间的相似度"""
    i, j, node_i, node_j, G, node_degrees = pair_data

    d_i = node_degrees[node_i]
    d_j = node_degrees[node_j]

    # 一步路径相似度
    sim1 = 1 / min(d_i, d_j) if G.has_edge(node_i, node_j) else 0

    # 两步路径相似度
    i_neighbors = set(G.neighbors(node_i))
    j_neighbors = set(G.neighbors(node_j))
    common_neighbors = i_neighbors.intersection(j_neighbors)
    sim2 = sum(1 / (min(d_i, d_j) * node_degrees[common])
              for common in common_neighbors)

    # 三步路径相似度（完整实现i→x→y→z=j的路径）
    sim3 = 0
    paths_found = set()

    for x in i_neighbors:
        d_x = node_degrees[x]
        for y in G.neighbors(x):
            if y == node_j or y == node_i:
                continue
            d_y = node_degrees[y]
            for z in G.neighbors(y):
                if z == node_j:  # 找到一个完整的三步路径
                    path_key = tuple(sorted([x, y]))
                    if path_key not in paths_found:
                        paths_found.add(path_key)
                        sim3 += 1 / (min(d_i, d_j) * d_x * d_y)

    # 计算总相似度
    total_sim = sim1 + sim2 + sim3
    return (i, j, total_sim)

def construct_similarity_matrix(G, nodes, num_cores=None):
    """
    使用TNS指标（公式5-8）构建节点相似度矩阵

    参数:
    G: 图对象
    nodes: 节点列表
    num_cores: 使用的CPU核心数，None表示使用所有可用核心
    """
    if num_cores is None:
        num_cores = multiprocessing.cpu_count()

    N = len(nodes)
    sim_matrix = np.zeros((N, N))

    # 预计算所有节点的度数
    node_degrees = {node: G.degree(node) for node in nodes}

    # 对角线元素设为1.0（自身相似度）
    np.fill_diagonal(sim_matrix, 1.0)

    # 如果节点数量很少或只有1个核心，使用串行计算
    if N < 1000 or num_cores <= 1:
        for i in range(N):
            node_i = nodes[i]
            for j in range(i+1, N):  # 只计算上三角矩阵
                node_j = nodes[j]

                d_i = node_degrees[node_i]
                d_j = node_degrees[node_j]

                # 一步路径相似度
                sim1 = 1 / min(d_i, d_j) if G.has_edge(node_i, node_j) else 0

                # 两步路径相似度
                i_neighbors = set(G.neighbors(node_i))
                j_neighbors = set(G.neighbors(node_j))
                common_neighbors = i_neighbors.intersection(j_neighbors)
                sim2 = sum(1 / (min(d_i, d_j) * node_degrees[common])
                          for common in common_neighbors)

                # 三步路径相似度（完整实现i→x→y→z=j的路径）
                sim3 = 0
                paths_found = set()

                for x in i_neighbors:
                    d_x = node_degrees[x]
                    for y in G.neighbors(x):
                        if y == node_j or y == node_i:
                            continue
                        d_y = node_degrees[y]
                        for z in G.neighbors(y):
                            if z == node_j:  # 找到一个完整的三步路径
                                path_key = tuple(sorted([x, y]))
                                if path_key not in paths_found:
                                    paths_found.add(path_key)
                                    sim3 += 1 / (min(d_i, d_j) * d_x * d_y)

                # 计算总相似度
                total_sim = sim1 + sim2 + sim3
                sim_matrix[i][j] = total_sim
                sim_matrix[j][i] = total_sim  # 利用对称性
    else:
        # 准备并行计算的数据
        pairs = []
        for i in range(N):
            node_i = nodes[i]
            for j in range(i+1, N):  # 只计算上三角矩阵
                node_j = nodes[j]
                pairs.append((i, j, node_i, node_j, G, node_degrees))

        # 使用进程池并行计算
        with ProcessPoolExecutor(max_workers=num_cores) as executor:
            results = list(executor.map(calculate_node_pair_similarity, pairs))

        # 整合结果到相似度矩阵
        for i, j, sim in results:
            sim_matrix[i][j] = sim
            sim_matrix[j][i] = sim  # 利用对称性

    return sim_matrix

# ====== 阶段3：递归聚类和种子选择 ======

def calculate_regional_influence(community, nodes, sim_matrix, potential, node_index):
    """
    计算社区内节点的区域影响力

    参数:
    community: 社区节点集合
    nodes: 所有节点列表
    sim_matrix: 相似度矩阵
    potential: 节点势能字典
    node_index: 节点索引映射字典
    """
    # 预计算社区节点索引
    comm_indices = [node_index[node] for node in community]

    # 使用矩阵切片计算
    comm_sim_matrix = sim_matrix[np.ix_(comm_indices, comm_indices)]
    np.fill_diagonal(comm_sim_matrix, 0)  # 排除自身
    ri = comm_sim_matrix.sum(axis=1)  # 向量化求和

    return {node: ri[i] for i, node in enumerate(community)}

def phase3_recursive_clustering(G, k, initial_centers, nodes, sim_matrix, potential):
    """阶段3：递归聚类和种子节点选择（算法3）"""
    actual_k = k  # 强制社区数量等于k
    communities = [set() for _ in range(actual_k)]
    centers = initial_centers.copy()

    # 预计算节点索引映射
    node_index = {node: idx for idx, node in enumerate(nodes)}

    # 初始节点分配到社区
    center_indices = [node_index[center] for center in centers]

    # 使用矩阵操作分配节点到社区
    for node in G.nodes():
        if node in centers:
            # 将每个中心点分配到自己的社区
            center_idx = centers.index(node)
            communities[center_idx].add(node)
        else:
            # 寻找最相似的中心点
            node_idx = node_index[node]
            similarities = [sim_matrix[node_idx][center_idx] for center_idx in center_indices]
            best_center_idx = np.argmax(similarities)
            communities[best_center_idx].add(node)

    # 递归聚类过程
    max_iter = 100
    no_change_count = 0

    for iteration in range(max_iter):
        old_centers = centers.copy()

        # 更新中心点索引
        center_indices = [node_index[center] for center in centers]

        # 重新分配节点到社区
        communities = [set([center]) for center in centers]

        # 使用矩阵操作加速节点分配
        for node in G.nodes():
            if node not in centers:
                node_idx = node_index[node]
                similarities = [sim_matrix[node_idx][center_idx] for center_idx in center_indices]
                best_center_idx = np.argmax(similarities)
                communities[best_center_idx].add(node)

        # 检查收敛
        if set(old_centers) == set(centers):
            no_change_count += 1
            if no_change_count >= 3:
                print(f"递归聚类收敛于迭代{iteration}次")
                break
        else:
            no_change_count = 0

    return centers[:k]

# ====== CIM算法主函数 ======

def find_influential_nodes(G, k, sigma=1.1243, num_cores=None):
    """
    使用CIM算法寻找有影响力的节点

    参数：
    -----------
    G : networkx.Graph
        社交网络图
    k : int
        要选择的种子节点数量
    sigma : float
        影响因子，调节每个节点的影响范围
    num_cores : int or None
        使用的CPU核心数，None表示使用所有可用核心

    返回：
    --------
    list
        选定的种子节点列表
    """
    # 如果未指定核心数，使用所有可用核心
    if num_cores is None:
        num_cores = multiprocessing.cpu_count()

    nodes = list(G.nodes())

    print(f"\n=== 开始CIM算法，目标选择 {k} 个种子节点，使用 {num_cores} 个CPU核心 ===")
    start_total = time.time()

    # 阶段1：计算节点属性并确定初始聚类中心
    print("\n[阶段1] 计算节点属性...")
    phase1_start = time.time()
    ks = calculate_kshell(G)
    mass = calculate_mass(G, ks, num_cores)
    potential = calculate_potential(G, mass, sigma, num_cores)
    node_types = categorize_nodes(G, potential, num_cores)
    initial_centers, synthetic_values = phase1_determine_initial_centers(G, k, potential, node_types)
    print(f"[阶段1] 完成，耗时 {time.time()-phase1_start:.2f} 秒")

    # 阶段2：构建节点相似度矩阵
    print("\n[阶段2] 构建相似度矩阵...")
    phase2_start = time.time()
    sim_matrix = construct_similarity_matrix(G, nodes, num_cores)
    print(f"[阶段2] 矩阵尺寸 {sim_matrix.shape}，耗时 {time.time()-phase2_start:.2f} 秒")

    # 阶段3：递归聚类和种子选择
    print("\n[阶段3] 开始递归聚类...")
    phase3_start = time.time()
    seeds = phase3_recursive_clustering(G, k, initial_centers, nodes, sim_matrix, potential)
    print(f"[阶段3] 完成聚类，耗时 {time.time()-phase3_start:.2f} 秒")

    print(f"\n=== 算法总耗时 {time.time()-start_total:.2f} 秒 ===")
    return seeds

if __name__ == "__main__":
    # 开始计时
    start_time = time.time()
    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\email.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"

    # network_path = "D:\\VS\\code\\networks\\deezer.txt"
    # network_path = "D:\\VS\\code\\networks\\pgp.txt"

    G = gen_graph(network_path)
    # 示例用法
    # G = gen_graph("D:\\VS\\code\\networks\\CA-HepTh.txt")

    # 设置要使用的CPU核心数
    # 可以根据系统情况调整，None表示使用所有可用核心
    num_cores = 4  # 使用4个核心，可以根据需要修改

    # 寻找有影响力的种子节点
    k = 50
    seeds = find_influential_nodes(G, k, num_cores=num_cores)
    print(f"选定的种子节点: 数量: {len(set(seeds))}, 节点: {seeds}")

    influence = IC(G, seeds, p=0.05, mc=1000)
    print(f"influence: {influence}")

    # 计算并输出总执行时间（秒）
    total_time = int(time.time() - start_time)
    print(f"算法执行时间: {total_time}秒")

    # 可视化结果（如需要请取消注释）
    # visualize_seed_set(G, seeds)

    # 在读取图数据后添加
    print(f"网络数据：节点数 {len(G.nodes())}, 边数 {len(G.edges())}")

    # 在影响力计算后添加
    print(f"influence: {influence:.2f} ")
