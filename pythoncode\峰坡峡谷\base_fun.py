import numpy as np
import networkx as nx
import random
import math

"""
图构建
IC模拟
EDV评估
LIE估计
基于节点度数初始化位置
基于局部桥接中心性（LBC）初始化位置
局部搜索

"""

# 从文件加载图形数据并生成无向图
def gen_graph(filename: str) -> nx.Graph:
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

# 影响力传播模拟 (IC模型)
def IC(g, seed, p, mc=1000):
    seed = set(seed)  # 转换为集合，避免重复元素
    influence = []
    for _ in range(mc):
        new_active, last_active = set(seed), set(seed)  # 使用集合来去重
        while new_active:
            new_ones = set()
            for node in new_active:
                node_neighbors = list(g.neighbors(node))  
                for neighbor in node_neighbors:
                    if np.random.uniform(0, 1) < p:
                        new_ones.add(neighbor)
            new_active = new_ones - last_active
            last_active.update(new_active)
        influence.append(len(last_active))  # 记录激活的总节点数
    return np.mean(influence)  # 返回平均影响力

# 影响力评估（EDV）
def EDV(graph, S, p=0.1, k=2, cached_neighbors=None):
    influence_sum = 0
    NS_1 = set()
    for node in S:
        if node in graph:
            NS_1.update(graph[node])  # 获取一阶邻居节点集合

    for node in NS_1:
        if node not in S:
            num_connections = sum(1 for s in S if s in graph[node])
            influence_sum += 1 - (1 - p) ** num_connections
    result = (k + influence_sum)    # 计算 EDV(S)

    return result  # 返回 EDV(S)

def lie(G, seed, p=0.1):
    """
    局部影响估计（LIE）函数，计算给定种子集合或单个节点的影响力。
    G: NetworkX 图对象
    seed: 种子集合（节点列表/集合）或单个节点
    p: 影响传播的概率
    """
    # 输入有效性检查
    if not G or not seed:
        return 0.0
        
    # 如果 seed 是单个节点，将其转换为集合
    if not isinstance(seed, (list, set)):
        if seed is None:
            return 0.0
        seed = {seed}
    elif isinstance(seed, list):
        seed = set(seed)
        
    # 过滤掉None节点
    seed = {node for node in seed if node is not None}
    if not seed:
        return 0.0

    k = len(seed)
    sigma_0 = k  # 初始种子集合的影响力（即种子的数量）
    sigma_1 = 0  # 一跳邻居的影响力
    sigma_2 = 0  # 二跳邻居的影响力

    # 一跳邻居
    N1 = set()
    for node in seed:
        N1.update(G.neighbors(node))

    # 计算一跳邻居的影响力
    for u in N1:
        if u not in seed:
            neighbors = list(G.neighbors(u))
            prob_sum = 1 - np.prod([(1 - p) for v in neighbors if v in seed])
            sigma_1 += prob_sum

    # 二跳邻居
    N2 = set()
    for node in N1:
        N2.update(G.neighbors(node))

    # 计算二跳邻居的影响力
    for u in N2:
        if u not in seed and u not in N1:
            neighbors = list(G.neighbors(u))
            d_u = len([v for v in neighbors if v in N1])  # 与一跳邻居的连接度
            if d_u > 0:
                prob_sum = 1 - np.prod([(1 - p) for v in neighbors if v in N1])
                sigma_2 += prob_sum * (d_u / len(N1))
    if len(seed) != k:
        print(f"种子集大小发生变化")
    # 返回总的影响力
    return sigma_0 + sigma_1 + sigma_2

# 基于节点度数初始化位置
def degree_initialization(G, n, k):
    """
    基于节点度数初始化位置。
    G: NetworkX 图对象
    n: 粒子数
    k: 种子集合大小
    """
    positions = []
    for _ in range(n):
        # 基于节点度数排序后，选择k个度数最大的节点作为初始位置
        degree_sorted_nodes = sorted(G.nodes(), key=lambda node: G.degree(node), reverse=True)
        position = degree_sorted_nodes[:k]
        for i in range(k):
            if random.random() > 0.5:  # 50%概率替换
                # 获取所有不在当前解中的节点
                available_nodes = [node for node in G.nodes() if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        positions.append(position)
    return positions

# 基于局部桥接中心性（LBC）初始化位置
def lbc_initialization(G, n, k):
    """
    基于局部桥接中心性（LBC）初始化位置。
    G: NetworkX 图对象
    n: 粒子数
    k: 种子集合大小
    """
    # 计算局部桥接中心性（LBC）
    def calculate_lbc(graph):
        lbc_scores = {}
        for node in graph.nodes():
            score = 0.0
            neighbors = list(graph.neighbors(node))
            for neighbor in neighbors:
                # 计算共同邻居数
                common_neighbors = len(list(nx.common_neighbors(graph, node, neighbor)))
                score += 1.0 / (common_neighbors + 1)
            lbc_scores[node] = score
        return lbc_scores
    
    # 获取LBC分数并排序
    lbc_scores = calculate_lbc(G)
    sorted_nodes = sorted(G.nodes(), key=lambda x: lbc_scores[x], reverse=True)
    
    positions = []
    for _ in range(n):
        # 选择LBC最高的前k个节点
        position = sorted_nodes[:k].copy()
        
        # 保持原有的随机扰动逻辑
        for i in range(k):
            if random.random() > 0.5:  # 50%概率替换
                available_nodes = [node for node in G.nodes() if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        
        positions.append(position)
    
    return positions


from typing import Union, Set, List
from functools import lru_cache

def cached_lie(G: nx.Graph, seed: Union[Set[int], List[int]], p: float) -> float:
    """带缓存的局部影响估计函数"""
    # 将种子集合转换为可哈希的元组
    seed_tuple = tuple(sorted(seed))
    return _cached_lie_internal(G, seed_tuple, p)

@lru_cache(maxsize=1024)
def _cached_lie_internal(G: nx.Graph, seed_tuple: tuple, p: float) -> float:
    """内部缓存函数，处理可哈希的种子元组"""
    return lie(G, set(seed_tuple), p)

#局部搜索
def local_search(xi, G, p, k):
    """局部搜索以优化种子节点"""
    # 缓存当前种子集合的影响力值
    xi_fitness = EDV(G, xi, p, k)
    
    for x_ij in list(xi):  # 遍历当前种子节点的副本
        neighbors = list(G.neighbors(x_ij))  # 获取当前节点的邻居
        for neighbor in neighbors:  # 遍历邻居节点
            if neighbor not in xi:  # 如果邻居不在当前种子节点中
                # 尝试替换
                xi_new = xi.copy()  # 创建当前种子节点的副本
                xi_new.remove(x_ij)  # 从副本中移除当前节点
                xi_new.append(neighbor)  # 添加邻居节点
                
                # 只在影响力提高时才进行更新
                # xi_new_fitness = EDV(G, xi_new, p, k)
                xi_new_fitness = lie(G, xi_new, p)

                if xi_new_fitness > xi_fitness:  
                    xi = xi_new  # 更新种子节点
                    xi_fitness = xi_new_fitness  # 更新当前种子集合的影响力值
                    break  # 退出邻居循环，尝试对下一个种子节点优化

    # if len(xi) != k:  
    #     print("局部搜索种子集大小发生变化")
    #     exit(0)
    
    return xi  # 返回优化后的种子节点

import matplotlib.pyplot as plt
#可视化种子集在网络中的分布
def visualize_seed_set(G, seed_set, title="Seed Set Distribution in Network"):
    """可视化种子节点在网络中的分布
    Args:
        G: 网络图
        seed_set: 种子节点集合
        title: 图表标题
    """
    plt.figure(figsize=(12, 8))
    pos = nx.spring_layout(G, k=1, iterations=50)  # 使用弹簧布局
    
    # 绘制所有边
    nx.draw_networkx_edges(G, pos, alpha=0.2, edge_color='gray')
    
    # 绘制非种子节点
    non_seed_nodes = list(set(G.nodes()) - set(seed_set))
    nx.draw_networkx_nodes(G, pos, nodelist=non_seed_nodes,
                          node_color='lightblue',
                          node_size=100,
                          alpha=0.6,
                          label='Normal Nodes')
    
    # 绘制种子节点
    nx.draw_networkx_nodes(G, pos, nodelist=seed_set,
                          node_color='red',
                          node_size=300,
                          alpha=0.8,
                          label='Seed Nodes')
    
    plt.title(title)
    plt.legend()
    plt.axis('off')
    plt.tight_layout()
    plt.show()